from typing import List, Callable, Dict, Any

class BaseAgent:
    # Default tools provided to all agents
    default_tools: List[Callable] = []
    access_specifier: str = "General"  # or "Confidential", etc.
    model_name: str = "gpt-4"
    system_prompt: str = ""

    def __init__(self, model_name: str = None, access_specifier: str = None):
        if model_name:
            self.model_name = model_name
        if access_specifier:
            self.access_specifier = access_specifier
        # Each agent gets its own copy of the default tools
        self._tools = list(self.default_tools)
        self.system_prompt = self._build_system_prompt()

    @property
    def tools(self) -> List[Callable]:
        return self._tools

    def _build_system_prompt(self) -> str:
        return f"System prompt for {self.model_name} with tools: {', '.join([t.__name__ for t in self.tools])}"

    def load_model(self):
        # Load or initialize the model (stub)
        pass

    def secure_get_api_key(self, service: str) -> str:
        # Securely fetch API key for a service (stub)
        return "FAKE_API_KEY"

    def reply(self, message: str, context: Dict[str, Any] = None) -> str:
        # Demo reply logic
        return f"[{self.model_name}] Received: {message} | Tools: {', '.join([t.__name__ for t in self.tools])}" 