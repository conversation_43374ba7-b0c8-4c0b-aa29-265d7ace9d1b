from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import Optional, Dict, Any
import shutil
import tempfile
import os
from pathlib import Path
from app.managers.ProjectManager import ProjectManager

router = APIRouter()

@router.post("/projects/{project_id}/documents/upload")
async def upload_document(
    project_id: str,
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(None)
):
    """
    Upload and process a document through the Graph-RAG pipeline.
    
    Args:
        project_id: The project ID to associate the document with
        file: The uploaded file
        metadata: Optional JSON string with document metadata
    
    Returns:
        Processing results
    """
    try:
        # Get the project
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Create a temporary file to store the upload
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp_file:
            shutil.copyfileobj(file.file, tmp_file)
            tmp_file_path = tmp_file.name
        
        try:
            # Parse metadata if provided
            doc_metadata = {}
            if metadata:
                import json
                doc_metadata = json.loads(metadata)
            
            # Add file information to metadata
            doc_metadata.update({
                "original_filename": file.filename,
                "content_type": file.content_type,
                "project_id": project_id
            })
            
            # Process the document through the agent manager
            result = project.agent_manager.process_document(tmp_file_path, doc_metadata)
            
            # Save the file to the project's input directory if processing was successful
            if result.get("status") == "success":
                saved_file_path = project.save_file(file.filename, tmp_file_path, location="input")
                result["saved_file_path"] = saved_file_path
            
            return result
            
        finally:
            # Clean up temporary file
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

@router.post("/projects/{project_id}/search/vector")
async def vector_search(
    project_id: str,
    query: str,
    k: int = 5
):
    """
    Perform vector similarity search within a project.
    
    Args:
        project_id: The project ID to search within
        query: The search query
        k: Number of results to return
    
    Returns:
        Search results
    """
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get the Graph-RAG agent
        graphrag_agent = project.agent_manager.get_agent("graphrag")
        if not graphrag_agent:
            raise HTTPException(status_code=500, detail="Graph-RAG agent not available")
        
        # Perform vector search
        result = graphrag_agent.vector_search_tool(query, k)
        
        return {"query": query, "results": result}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error performing vector search: {str(e)}")

@router.post("/projects/{project_id}/search/knowledge-graph")
async def knowledge_graph_search(
    project_id: str,
    subject: Optional[str] = None,
    predicate: Optional[str] = None,
    obj: Optional[str] = None
):
    """
    Query the knowledge graph within a project.
    
    Args:
        project_id: The project ID to search within
        subject: Subject entity to search for
        predicate: Relationship/predicate to search for
        obj: Object entity to search for
    
    Returns:
        Knowledge graph query results
    """
    try:
        if not any([subject, predicate, obj]):
            raise HTTPException(status_code=400, detail="At least one of subject, predicate, or object must be provided")
        
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get the Graph-RAG agent
        graphrag_agent = project.agent_manager.get_agent("graphrag")
        if not graphrag_agent:
            raise HTTPException(status_code=500, detail="Graph-RAG agent not available")
        
        # Perform knowledge graph query
        result = graphrag_agent.knowledge_graph_query_tool(subject, predicate, obj)
        
        return {
            "query": {
                "subject": subject,
                "predicate": predicate,
                "object": obj
            },
            "results": result
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error querying knowledge graph: {str(e)}")

@router.post("/projects/{project_id}/search/hybrid")
async def hybrid_search(
    project_id: str,
    query: str,
    k: int = 5
):
    """
    Perform hybrid search (vector + knowledge graph) within a project.
    
    Args:
        project_id: The project ID to search within
        query: The search query
        k: Number of vector results to return
    
    Returns:
        Combined search results
    """
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get the Graph-RAG agent
        graphrag_agent = project.agent_manager.get_agent("graphrag")
        if not graphrag_agent:
            raise HTTPException(status_code=500, detail="Graph-RAG agent not available")
        
        # Perform hybrid search
        result = graphrag_agent.hybrid_search_tool(query, k)
        
        return {"query": query, "results": result}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error performing hybrid search: {str(e)}")

@router.get("/projects/{project_id}/graphrag/stats")
async def get_graphrag_stats(project_id: str):
    """
    Get Graph-RAG statistics for a project.
    
    Args:
        project_id: The project ID
    
    Returns:
        Graph-RAG statistics
    """
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get stats from agent manager
        stats = project.agent_manager.get_agent_stats("graphrag")
        
        return {
            "project_id": project_id,
            "graphrag_stats": stats
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting Graph-RAG stats: {str(e)}")
