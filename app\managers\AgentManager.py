class AgentManager:
    def __init__(self):
        self.agents = {}  # name -> agent instance

    def register_agent(self, name, agent_instance):
        self.agents[name] = agent_instance

    def list_agents(self):
        return list(self.agents.keys())

    def generate_response(self, message, context=None):
        # strategy to decide which agent to use 
        # have a reply
        # for now just echo the message
        return {"recieved": message};
