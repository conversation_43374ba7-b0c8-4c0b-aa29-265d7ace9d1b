#!/usr/bin/env python3
"""
Test script to verify vector search is working.
"""

import os
from app.managers.DocumentManager import Document<PERSON>ana<PERSON>

def test_vector_search():
    """Test if vector search is working properly."""
    
    print("🧪 Testing vector search functionality...")
    
    try:
        # Create DocumentManager
        doc_manager = DocumentManager(project_id="test")
        
        if not doc_manager.vector_store:
            print("❌ Vector store not initialized")
            return False
        
        print("✅ Vector store initialized")
        
        # Test adding a simple document
        print("📝 Adding test document...")
        test_content = """
        IVF (In Vitro Fertilization) is a medical procedure used to help with fertility issues.
        The IVF process involves several steps:
        1. Ovarian stimulation with hormones
        2. Egg retrieval from the ovaries
        3. Fertilization in the laboratory
        4. Embryo transfer to the uterus
        
        The success rate of IVF varies but is typically around 40-50% for women under 35.
        IVF treatment can take several weeks and may require multiple cycles.
        """
        
        result = doc_manager.process_text_content(
            test_content, 
            "test_ivf_document.txt", 
            {"test": True}
        )
        
        print(f"📄 Document processing result: {result}")
        
        if result.get("status") != "success":
            print("❌ Document processing failed")
            return False
        
        print("✅ Document processed successfully")
        
        # Test vector search
        print("🔍 Testing vector search...")
        
        test_queries = [
            "IVF process",
            "how to use IVF", 
            "fertility treatment",
            "embryo transfer",
            "success rate"
        ]
        
        for query in test_queries:
            print(f"\n🔎 Searching for: '{query}'")
            search_results = doc_manager.vector_search(query, k=3)
            
            print(f"   Found {len(search_results)} results")
            
            for i, result in enumerate(search_results):
                print(f"   {i+1}. Score: {result['score']:.3f}")
                print(f"      Content: {result['content'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_vector_search()
    if success:
        print("\n🎉 Vector search test completed!")
    else:
        print("\n💥 Vector search test failed!")
