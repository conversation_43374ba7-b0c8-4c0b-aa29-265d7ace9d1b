import os
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
from .ConversationManager import ConversationManager
from .FileManager import FileManager
from .AgentManager import AgentManager

class ProjectManager:
    """Project manager with unified file and git management via FileManager."""
    
    def __init__(self, project_name: str = None, base_dir: str = "./projects", project_id: str = None):
        self.project_id = project_id or str(uuid.uuid4())
        self.project_name = project_name or f"project_{self.project_id[:8]}"
        self.base_dir = Path(base_dir)
        self.project_path = self.base_dir / self.project_id  # Use project_id for folder name
        
        # Managers
        self.file_manager = FileManager(self.project_path)
        self.agent_manager = AgentManager()
        
        # Initialize
        if self.project_path.exists():
            self._load_existing()
        else:
            self._create_new()

    @classmethod
    def from_project_id(cls, project_id: str, base_dir: str = "./projects") -> Optional["ProjectManager"]:
        project_path = Path(base_dir) / project_id
        if not (project_path.exists() and project_path.is_dir()):
            return None
        return cls(project_id=project_id, base_dir=base_dir)
    
    def _create_new(self):
        self.file_manager.create_new_project(self.project_id, self.project_name)
        self.conversation_manager = ConversationManager(self.project_path / "conversation.json")
        self.file_manager.commit("🎉 Initial project setup")
    
    def _load_existing(self):
        metadata, messages = self.file_manager.load_existing_project()
        if metadata:
            self.project_id = metadata.get("project_id", self.project_id)
            self.project_name = metadata.get("project_name", self.project_name)
        self.conversation_manager = ConversationManager(self.project_path / "conversation.json")
        self.conversation_manager.messages = messages

    # THE IMPORTANT FUNCTION 
    
    def talk(self, message: str, role: str = "user") -> str:
        # Add user message (no commit hash yet)
        self.conversation_manager.add_message(role, message)
        # Generate agent reply and add agent message (no commit hash yet)
        reply = self.agent_manager.generate_response(message)
        self.conversation_manager.add_message("agent", reply)
        # Commit after both messages are added
        commit_msg = f"💬 {role}: {str(message)[:50]}{'...' if len(message) > 50 else ''} | 🤖 agent: {str(reply)[:50]}{'...' if len(reply) > 50 else ''}"
        commit_hash = self.file_manager.commit(commit_msg)
        # Update the last two messages in memory with the commit hash
        self.conversation_manager._update_message_commit_hash(commit_hash=commit_hash)
        # Save only the new/updated messages to storage
        self.conversation_manager._save_conversation()
        return reply

    def add_message(self, role: str, content: str) -> str:
        # Add message without commit_hash first
        message_id = self.conversation_manager.add_message(role, content)
        commit_msg = f"💬 {role}: {content[:50]}{'...' if len(content) > 50 else ''}"
        commit_hash = self.file_manager.commit(commit_msg)
        # Update the message with commit_hash
        for msg in self.conversation_manager.messages:
            if msg["id"] == message_id:
                msg["commit_hash"] = commit_hash
                break
        self.conversation_manager._save_conversation()
        return message_id
    
    def edit_message(self, message_index: int, new_content: str) -> str:
        if not self.conversation_manager.edit_message(message_index, new_content):
            raise ValueError(f"Message index {message_index} out of range")
        branch_name = f"edit_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        try:
            self.file_manager.create_branch(branch_name)
            self.file_manager.checkout_branch(branch_name)
            self.conversation_manager._save_conversation()
            commit_msg = f"✏ Edit: {new_content[:50]}{'...' if len(new_content) > 50 else ''}"
            self.file_manager.commit(commit_msg)
            return branch_name
        except Exception as e:
            return None
    
    def get_conversation(self) -> List[Dict]:
        return self.conversation_manager.get_conversation()
    
    def save_file(self, filename: str, content, location: str = "output"):
        return self.file_manager.save_file(filename, content, location)
    
    def get_project_status(self) -> Dict:
        return {
            "project_name": self.project_name,
            "project_id": self.project_id,
            "current_branch": self.file_manager.current_branch,
            "available_branches": self.file_manager.list_branches(),
            "message_count": len(self.get_conversation()),
            "last_commit": self.file_manager.repo.head.commit.hexsha[:8] if self.file_manager.repo else None,
            "input_files": self.file_manager.list_files("input"),
            "output_files": self.file_manager.list_files("output")
        }

  
