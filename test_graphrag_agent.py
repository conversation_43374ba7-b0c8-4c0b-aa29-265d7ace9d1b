#!/usr/bin/env python3
"""
Test script to verify GraphRAG agent is working with project_search tool.
"""

import os
from app.agents.GraphRAGAgent import GraphRAGAgent

def test_graphrag_agent():
    """Test if GraphRAG agent is working properly."""
    
    print("🤖 Testing GraphRAG agent functionality...")
    
    try:
        # Set a dummy OpenAI API key for testing (won't actually call OpenAI)
        if not os.getenv("OPENAI_API_KEY"):
            print("⚠️  No OpenAI API key set - this test will show tool availability only")
        
        # Create GraphRAG agent
        agent = GraphRAGAgent(project_id="test")
        
        print("✅ GraphRAG agent initialized")
        
        # Check if tools are properly registered
        print(f"🛠️  Available tools: {[tool.__name__ for tool in agent.tools]}")
        
        # Test the project_search tool directly
        print("🔍 Testing project_search tool...")
        
        search_result = agent.project_search("final accuracy", k=3)
        print(f"📄 Search result: {search_result}")
        
        # Test agent stats
        stats = agent.get_stats()
        print(f"📊 Agent stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_graphrag_agent()
    if success:
        print("\n🎉 GraphRAG agent test completed!")
    else:
        print("\n💥 GraphRAG agent test failed!")
