#!/usr/bin/env python3
"""
Reset the vector database to fix schema issues.
Run this script to clean up and recreate the vector database tables.
"""

import os
import psycopg2
from app.managers.DocumentManager import DocumentManager

def reset_vector_database():
    """Reset the vector database tables."""
    
    # Database config
    db_config = {
        "host": os.getenv("POSTGRES_HOST", "localhost"),
        "port": os.getenv("POSTGRES_PORT", "5432"),
        "database": os.getenv("POSTGRES_DB", "kairos_graphrag"),
        "user": os.getenv("POSTGRES_USER", "postgres"),
        "password": os.getenv("POSTGRES_PASSWORD", "password")
    }
    
    try:
        print("🗄️  Connecting to PostgreSQL...")
        conn = psycopg2.connect(**db_config)
        cur = conn.cursor()
        
        print("🧹 Dropping old vector tables...")
        # Drop all langchain tables
        cur.execute("DROP TABLE IF EXISTS langchain_pg_embedding CASCADE;")
        cur.execute("DROP TABLE IF EXISTS langchain_pg_collection CASCADE;")
        
        # Also drop any old tables that might exist
        cur.execute("DROP TABLE IF EXISTS langchain_pg_embedding_store CASCADE;")
        
        print("✅ Old tables dropped successfully")
        
        conn.commit()
        cur.close()
        conn.close()
        
        print("🔧 Creating new DocumentManager to initialize tables...")
        # Create a DocumentManager instance - this will create the new tables
        doc_manager = DocumentManager(project_id="test")
        
        print("✅ Vector database reset complete!")
        print("\nNext steps:")
        print("1. Upload documents to your project")
        print("2. Test the search functionality")
        
    except Exception as e:
        print(f"❌ Error resetting database: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Resetting vector database...")
    print("=" * 50)
    
    success = reset_vector_database()
    
    if success:
        print("\n🎉 Database reset completed successfully!")
    else:
        print("\n💥 Database reset failed!")
