from typing import List, Callable, Dict, Any, Optional
from .BaseAgent import BaseAgent
from ..managers.DocumentManager import DocumentManager
import json

class GraphRAGAgent(BaseAgent):
    """Graph-RAG Agent that extends BaseAgent with vector search and knowledge graph capabilities."""
    
    access_specifier: str = "General"
    model_name: str = "test"
    
    def __init__(self, project_id: str = None, model_name: str = None, access_specifier: str = None, db_config: Dict = None):
        # Initialize the document manager
        self.project_id = project_id
        self.document_manager = DocumentManager(project_id=project_id, db_config=db_config)
        
        # Define tools specific to Graph-RAG
        self.default_tools = [
            self.vector_search_tool,
            self.knowledge_graph_query_tool,
            self.hybrid_search_tool,
            self.process_document_tool
        ]
        
        # Call parent constructor
        super().__init__(model_name=model_name, access_specifier=access_specifier)
    
    def _build_system_prompt(self) -> str:
        return f"""You are a Graph-RAG AI assistant with access to both vector search and knowledge graph capabilities.

You have access to the following tools:
- vector_search: Search for similar document chunks using semantic similarity
- knowledge_graph_query: Query structured knowledge graph for factual relationships
- hybrid_search: Combine both vector and knowledge graph search
- process_document: Process new documents through the Graph-RAG pipeline

Use vector search for open-ended or contextual questions.
Use knowledge graph queries for factual questions about specific entities and relationships.
Use hybrid search when you need comprehensive information combining both approaches.

Model: {self.model_name}
Access Level: {self.access_specifier}
Project ID: {self.project_id}
"""
    
    def vector_search_tool(self, query: str, k: int = 5) -> str:
        """
        Search for similar document chunks using vector similarity.
        
        Args:
            query: The search query
            k: Number of results to return (default: 5)
        
        Returns:
            JSON string with search results
        """
        try:
            results = self.document_manager.vector_search(query, k)
            return json.dumps({
                "tool": "vector_search",
                "query": query,
                "results": results,
                "count": len(results)
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "vector_search",
                "error": str(e)
            })
    
    def knowledge_graph_query_tool(self, subject: str = None, predicate: str = None, obj: str = None) -> str:
        """
        Query the knowledge graph for specific relationships.
        
        Args:
            subject: Subject entity to search for
            predicate: Relationship/predicate to search for
            obj: Object entity to search for
        
        Returns:
            JSON string with knowledge graph results
        """
        try:
            results = self.document_manager.knowledge_graph_query(subject, predicate, obj)
            return json.dumps({
                "tool": "knowledge_graph_query",
                "query": {
                    "subject": subject,
                    "predicate": predicate,
                    "object": obj
                },
                "results": results,
                "count": len(results)
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "knowledge_graph_query",
                "error": str(e)
            })
    
    def hybrid_search_tool(self, query: str, k: int = 5) -> str:
        """
        Perform both vector search and knowledge graph query.
        
        Args:
            query: The search query
            k: Number of vector results to return
        
        Returns:
            JSON string with combined results
        """
        try:
            results = self.document_manager.hybrid_search(query, k)
            return json.dumps({
                "tool": "hybrid_search",
                "query": query,
                "vector_results": results["vector_results"],
                "knowledge_graph_results": results["knowledge_graph_results"],
                "total_vector_results": len(results["vector_results"]),
                "total_kg_results": len(results["knowledge_graph_results"])
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "hybrid_search",
                "error": str(e)
            })
    
    def process_document_tool(self, file_path: str, metadata: Dict = None) -> str:
        """
        Process a document through the Graph-RAG pipeline.
        
        Args:
            file_path: Path to the document to process
            metadata: Optional metadata to attach to the document
        
        Returns:
            JSON string with processing results
        """
        try:
            results = self.document_manager.process_document(file_path, metadata)
            return json.dumps({
                "tool": "process_document",
                "file_path": file_path,
                "metadata": metadata,
                "results": results
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "process_document",
                "error": str(e)
            })
    
    def reply(self, message: str, context: Dict[str, Any] = None) -> str:
        """Generate a proper response using Graph-RAG capabilities."""
        try:
            # Perform hybrid search to get relevant information
            search_results = self.document_manager.hybrid_search(message)

            vector_results = search_results["vector_results"]
            kg_results = search_results["knowledge_graph_results"]

            # If we have relevant information, use it to generate a response
            if vector_results or kg_results:
                response_parts = []

                # Use vector search results for context
                if vector_results:
                    relevant_content = []
                    for result in vector_results[:3]:  # Top 3 most relevant
                        relevant_content.append(result['content'])

                    # Create a simple response based on the content
                    if "accuracy" in message.lower():
                        # Look for accuracy-related information in the content
                        for content in relevant_content:
                            if any(word in content.lower() for word in ["accuracy", "precise", "correct", "performance", "score"]):
                                response_parts.append(f"Based on the documents: {content[:300]}...")
                                break
                    else:
                        # General response using the most relevant content
                        response_parts.append(f"Based on the available information: {relevant_content[0][:300]}...")

                # Add knowledge graph information if available
                if kg_results:
                    facts = []
                    for kg in kg_results[:3]:
                        facts.append(f"{kg['subject']} {kg['predicate']} {kg['object']}")
                    if facts:
                        response_parts.append(f"Related facts: {'; '.join(facts)}")

                if response_parts:
                    return "\n\n".join(response_parts)

            # Fallback response when no relevant information is found
            return f"I don't have specific information about '{message}' in the current knowledge base. You may need to upload relevant documents first."

        except Exception as e:
            return f"Error processing your question: {str(e)}"
    
    def get_stats(self) -> Dict:
        """Get statistics about the Graph-RAG system."""
        try:
            # Get vector store stats (this would need to be implemented in DocumentManager)
            # Get knowledge graph stats
            kg_results = self.document_manager.knowledge_graph_query()  # Get all triples
            
            return {
                "project_id": self.project_id,
                "model_name": self.model_name,
                "total_triples": len(kg_results),
                "tools_available": len(self.tools),
                "tool_names": [t.__name__ for t in self.tools]
            }
        except Exception as e:
            return {
                "error": str(e)
            }
