from typing import List, Callable, Dict, Any, Optional
from .BaseAgent import BaseAgent
from ..managers.DocumentManager import DocumentManager
import json
import os

# LLM and tool imports
from langchain_openai import ChatOpenAI
from langchain.tools import tool
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import HumanMessage

class GraphRAGAgent(BaseAgent):
    """Graph-RAG Agent that extends BaseAgent with vector search and knowledge graph capabilities."""
    
    access_specifier: str = "General"
    model_name: str = "gpt-4o-mini"  # Use faster model for better response times

    def __init__(self, project_id: str = None, model_name: str = None, access_specifier: str = None, db_config: Dict = None):
        # Initialize the document manager
        self.project_id = project_id
        self.document_manager = DocumentManager(project_id=project_id, db_config=db_config)

        # Initialize LLM
        self.llm = ChatOpenAI(
            model=model_name or self.model_name,
            temperature=0.1,
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )

        # Create tools for the agent
        self.tools = [
            self._create_vector_search_tool(),
            self._create_hybrid_search_tool()
        ]

        # Create agent prompt
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", self._build_system_prompt()),
            MessagesPlaceholder(variable_name="chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])

        # Create agent
        self.agent = create_openai_functions_agent(self.llm, self.tools, self.prompt)
        self.agent_executor = AgentExecutor(agent=self.agent, tools=self.tools, verbose=True)

        # Call parent constructor
        super().__init__(model_name=model_name, access_specifier=access_specifier)
    
    def _build_system_prompt(self) -> str:
        return f"""You are a helpful AI assistant with access to a knowledge base through vector search capabilities.

You have access to tools that can search through uploaded documents to find relevant information.
When a user asks a question, use the search tools to find relevant information from the knowledge base.

Always provide helpful, accurate responses based on the information found in the documents.
If no relevant information is found, let the user know and suggest they upload relevant documents.

Project ID: {self.project_id}
Model: {self.model_name}
"""

    def _create_vector_search_tool(self):
        """Create vector search tool for the agent."""
        @tool
        def vector_search(query: str, k: int = 5) -> str:
            """
            Search for similar document chunks using vector similarity.

            Args:
                query: The search query
                k: Number of results to return (default: 5)

            Returns:
                Relevant document chunks
            """
            try:
                results = self.document_manager.vector_search(query, k)
                if not results:
                    return "No relevant documents found for this query."

                response_parts = []
                for i, result in enumerate(results[:3], 1):
                    response_parts.append(f"{i}. {result['content'][:300]}...")

                return "\n\n".join(response_parts)
            except Exception as e:
                return f"Error searching documents: {str(e)}"

        return vector_search

    def _create_hybrid_search_tool(self):
        """Create hybrid search tool for the agent."""
        @tool
        def hybrid_search(query: str, k: int = 5) -> str:
            """
            Perform comprehensive search through the knowledge base.

            Args:
                query: The search query
                k: Number of results to return

            Returns:
                Relevant information from documents
            """
            try:
                results = self.document_manager.hybrid_search(query, k)
                vector_results = results.get("vector_results", [])

                if not vector_results:
                    return "No relevant information found in the knowledge base."

                response_parts = []
                for i, result in enumerate(vector_results[:3], 1):
                    response_parts.append(f"Document {i}: {result['content'][:400]}...")

                return "\n\n".join(response_parts)
            except Exception as e:
                return f"Error searching knowledge base: {str(e)}"

        return hybrid_search

    def vector_search_tool(self, query: str, k: int = 5) -> str:
        """
        Search for similar document chunks using vector similarity.
        
        Args:
            query: The search query
            k: Number of results to return (default: 5)
        
        Returns:
            JSON string with search results
        """
        try:
            results = self.document_manager.vector_search(query, k)
            return json.dumps({
                "tool": "vector_search",
                "query": query,
                "results": results,
                "count": len(results)
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "vector_search",
                "error": str(e)
            })
    
    def knowledge_graph_query_tool(self, subject: str = None, predicate: str = None, obj: str = None) -> str:
        """
        Query the knowledge graph for specific relationships.
        
        Args:
            subject: Subject entity to search for
            predicate: Relationship/predicate to search for
            obj: Object entity to search for
        
        Returns:
            JSON string with knowledge graph results
        """
        try:
            results = self.document_manager.knowledge_graph_query(subject, predicate, obj)
            return json.dumps({
                "tool": "knowledge_graph_query",
                "query": {
                    "subject": subject,
                    "predicate": predicate,
                    "object": obj
                },
                "results": results,
                "count": len(results)
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "knowledge_graph_query",
                "error": str(e)
            })
    
    def hybrid_search_tool(self, query: str, k: int = 5) -> str:
        """
        Perform both vector search and knowledge graph query.
        
        Args:
            query: The search query
            k: Number of vector results to return
        
        Returns:
            JSON string with combined results
        """
        try:
            results = self.document_manager.hybrid_search(query, k)
            return json.dumps({
                "tool": "hybrid_search",
                "query": query,
                "vector_results": results["vector_results"],
                "knowledge_graph_results": results["knowledge_graph_results"],
                "total_vector_results": len(results["vector_results"]),
                "total_kg_results": len(results["knowledge_graph_results"])
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "hybrid_search",
                "error": str(e)
            })
    
    def process_document_tool(self, file_path: str, metadata: Dict = None) -> str:
        """
        Process a document through the Graph-RAG pipeline.
        
        Args:
            file_path: Path to the document to process
            metadata: Optional metadata to attach to the document
        
        Returns:
            JSON string with processing results
        """
        try:
            results = self.document_manager.process_document(file_path, metadata)
            return json.dumps({
                "tool": "process_document",
                "file_path": file_path,
                "metadata": metadata,
                "results": results
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "process_document",
                "error": str(e)
            })
    
    def reply(self, message: str, context: Dict[str, Any] = None) -> str:
        """Generate a response using the LLM agent with tool calls."""
        try:
            # Use the agent executor to generate a response
            response = self.agent_executor.invoke({"input": message})
            return response.get("output", "I couldn't generate a response.")

        except Exception as e:
            # Fallback to simple search if agent fails
            try:
                search_results = self.document_manager.hybrid_search(message)
                vector_results = search_results.get("vector_results", [])

                if vector_results:
                    return f"Based on the documents: {vector_results[0]['content'][:500]}..."
                else:
                    return f"I don't have specific information about '{message}' in the current knowledge base. You may need to upload relevant documents first."
            except:
                return f"Error processing your question: {str(e)}"

    def process_document_tool(self, file_content: bytes, filename: str, metadata: Dict = None) -> str:
        """Process a document through the Graph-RAG pipeline."""
        try:
            result = self.document_manager.process_file_upload(file_content, filename, metadata)
            return json.dumps(result, indent=2)
        except Exception as e:
            return json.dumps({
                "status": "error",
                "error": str(e)
            })

    def get_stats(self) -> Dict:
        """Get statistics about the Graph-RAG system."""
        try:
            # Get vector store stats (this would need to be implemented in DocumentManager)
            # Get knowledge graph stats
            kg_results = self.document_manager.knowledge_graph_query()  # Get all triples
            
            return {
                "project_id": self.project_id,
                "model_name": self.model_name,
                "total_triples": len(kg_results),
                "tools_available": len(self.tools),
                "tool_names": [t.__name__ for t in self.tools]
            }
        except Exception as e:
            return {
                "error": str(e)
            }
