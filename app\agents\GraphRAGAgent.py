from typing import List, Callable, Dict, Any, Optional
from .BaseAgent import BaseAgent
from ..managers.DocumentManager import DocumentManager
import json

class GraphRAGAgent(BaseAgent):
    """Graph-RAG Agent that extends BaseAgent with vector search and knowledge graph capabilities."""
    
    access_specifier: str = "General"
    model_name: str = "test"
    
    def __init__(self, project_id: str = None, model_name: str = None, access_specifier: str = None, db_config: Dict = None):
        # Initialize the document manager
        self.project_id = project_id
        self.document_manager = DocumentManager(project_id=project_id, db_config=db_config)
        
        # Define tools specific to Graph-RAG
        self.default_tools = [
            self.vector_search_tool,
            self.knowledge_graph_query_tool,
            self.hybrid_search_tool,
            self.process_document_tool
        ]
        
        # Call parent constructor
        super().__init__(model_name=model_name, access_specifier=access_specifier)
    
    def _build_system_prompt(self) -> str:
        return f"""You are a Graph-RAG AI assistant with access to both vector search and knowledge graph capabilities.

You have access to the following tools:
- vector_search: Search for similar document chunks using semantic similarity
- knowledge_graph_query: Query structured knowledge graph for factual relationships
- hybrid_search: Combine both vector and knowledge graph search
- process_document: Process new documents through the Graph-RAG pipeline

Use vector search for open-ended or contextual questions.
Use knowledge graph queries for factual questions about specific entities and relationships.
Use hybrid search when you need comprehensive information combining both approaches.

Model: {self.model_name}
Access Level: {self.access_specifier}
Project ID: {self.project_id}
"""
    
    def vector_search_tool(self, query: str, k: int = 5) -> str:
        """
        Search for similar document chunks using vector similarity.
        
        Args:
            query: The search query
            k: Number of results to return (default: 5)
        
        Returns:
            JSON string with search results
        """
        try:
            results = self.document_manager.vector_search(query, k)
            return json.dumps({
                "tool": "vector_search",
                "query": query,
                "results": results,
                "count": len(results)
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "vector_search",
                "error": str(e)
            })
    
    def knowledge_graph_query_tool(self, subject: str = None, predicate: str = None, obj: str = None) -> str:
        """
        Query the knowledge graph for specific relationships.
        
        Args:
            subject: Subject entity to search for
            predicate: Relationship/predicate to search for
            obj: Object entity to search for
        
        Returns:
            JSON string with knowledge graph results
        """
        try:
            results = self.document_manager.knowledge_graph_query(subject, predicate, obj)
            return json.dumps({
                "tool": "knowledge_graph_query",
                "query": {
                    "subject": subject,
                    "predicate": predicate,
                    "object": obj
                },
                "results": results,
                "count": len(results)
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "knowledge_graph_query",
                "error": str(e)
            })
    
    def hybrid_search_tool(self, query: str, k: int = 5) -> str:
        """
        Perform both vector search and knowledge graph query.
        
        Args:
            query: The search query
            k: Number of vector results to return
        
        Returns:
            JSON string with combined results
        """
        try:
            results = self.document_manager.hybrid_search(query, k)
            return json.dumps({
                "tool": "hybrid_search",
                "query": query,
                "vector_results": results["vector_results"],
                "knowledge_graph_results": results["knowledge_graph_results"],
                "total_vector_results": len(results["vector_results"]),
                "total_kg_results": len(results["knowledge_graph_results"])
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "hybrid_search",
                "error": str(e)
            })
    
    def process_document_tool(self, file_path: str, metadata: Dict = None) -> str:
        """
        Process a document through the Graph-RAG pipeline.
        
        Args:
            file_path: Path to the document to process
            metadata: Optional metadata to attach to the document
        
        Returns:
            JSON string with processing results
        """
        try:
            results = self.document_manager.process_document(file_path, metadata)
            return json.dumps({
                "tool": "process_document",
                "file_path": file_path,
                "metadata": metadata,
                "results": results
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "process_document",
                "error": str(e)
            })
    
    def reply(self, message: str, context: Dict[str, Any] = None) -> str:
        """
        Generate a reply using Graph-RAG capabilities.
        
        This is a simple implementation. In a real scenario, you would:
        1. Analyze the message to determine the best search strategy
        2. Use the appropriate tools (vector search, KG query, or hybrid)
        3. Generate a response based on the retrieved information
        """
        try:
            # For now, perform a hybrid search to get comprehensive information
            search_results = self.document_manager.hybrid_search(message)
            
            # Simple response generation (in practice, you'd use an LLM here)
            vector_count = len(search_results["vector_results"])
            kg_count = len(search_results["knowledge_graph_results"])
            
            response = f"[{self.model_name}] Graph-RAG Response to: {message}\n\n"
            
            if vector_count > 0:
                response += f"Found {vector_count} relevant document chunks:\n"
                for i, result in enumerate(search_results["vector_results"][:3]):  # Show top 3
                    response += f"{i+1}. {result['content'][:200]}...\n"
                response += "\n"
            
            if kg_count > 0:
                response += f"Found {kg_count} knowledge graph relationships:\n"
                for i, result in enumerate(search_results["knowledge_graph_results"][:3]):  # Show top 3
                    response += f"{i+1}. {result['subject']} -> {result['predicate']} -> {result['object']}\n"
                response += "\n"
            
            if vector_count == 0 and kg_count == 0:
                response += "No relevant information found in the knowledge base.\n"
            
            response += f"Tools available: {', '.join([t.__name__ for t in self.tools])}"
            
            return response
            
        except Exception as e:
            return f"[{self.model_name}] Error processing message: {str(e)}"
    
    def get_stats(self) -> Dict:
        """Get statistics about the Graph-RAG system."""
        try:
            # Get vector store stats (this would need to be implemented in DocumentManager)
            # Get knowledge graph stats
            kg_results = self.document_manager.knowledge_graph_query()  # Get all triples
            
            return {
                "project_id": self.project_id,
                "model_name": self.model_name,
                "total_triples": len(kg_results),
                "tools_available": len(self.tools),
                "tool_names": [t.__name__ for t in self.tools]
            }
        except Exception as e:
            return {
                "error": str(e)
            }
