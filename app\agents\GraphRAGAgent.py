from typing import Callable, Dict, Any
from .BaseAgent import BaseAgent
from ..managers.DocumentManager import DocumentManager
import json
import os
from openai import OpenAI

class GraphRAGAgent(BaseAgent):
    """Graph-RAG Agent that extends BaseAgent with vector search and knowledge graph capabilities."""
    
    access_specifier: str = "General"
    model_name: str = "gpt-4"
    
    def __init__(self, project_id: str = None, model_name: str = None, access_specifier: str = None, db_config: Dict = None):
        self.project_id = project_id
        self.document_manager = DocumentManager(project_id=project_id, db_config=db_config)
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Define tools - simple and clean
        self.default_tools = [self.search_documents, self.get_more_chunks]

        super().__init__(model_name=model_name, access_specifier=access_specifier)
    
    def _build_system_prompt(self) -> str:
        return f"""You are a helpful AI assistant with access to document search tools.

Available tools:
- search_documents: Search through uploaded documents
- get_more_chunks: Get additional document chunks if needed

Always search for relevant information before answering questions.
Model: {self.model_name} | Project: {self.project_id}
"""
    
    def search_documents(self, query: str, k: int = 5) -> str:
        """Search through documents and return results."""
        try:
            results = self.document_manager.vector_search(query, k)
            return json.dumps({
                "found": len(results),
                "results": [{"content": r["content"], "score": r["score"]} for r in results]
            })
        except Exception as e:
            return f"Search error: {str(e)}"

    def get_more_chunks(self, query: str, k: int = 10) -> str:
        """Get more document chunks for additional context."""
        try:
            results = self.document_manager.vector_search(query, k)
            return json.dumps({
                "additional_chunks": len(results),
                "content": [r["content"] for r in results]
            })
        except Exception as e:
            return f"Error getting more chunks: {str(e)}"
    
    def reply(self, message: str, context: Dict[str, Any] = None) -> str:
        """Clean, simple reply with LLM and tools."""
        try:
            # Search for relevant documents first
            search_results = self.document_manager.vector_search(message, k=5)

            # Debug info - show what we found
            debug_info = f"🔍 Found {len(search_results)} relevant chunks\n"
            if search_results:
                debug_info += f"📄 Top result score: {search_results[0]['score']:.3f}\n"

            # Prepare context for LLM
            context_text = ""
            if search_results:
                context_text = "\n\n".join([r["content"] for r in search_results])

            # Define tools for LLM
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "search_documents",
                        "description": "Search for more specific information in documents",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "query": {"type": "string", "description": "Search query"},
                                "k": {"type": "integer", "description": "Number of results (default: 5)"}
                            },
                            "required": ["query"]
                        }
                    }
                },
                {
                    "type": "function",
                    "function": {
                        "name": "get_more_chunks",
                        "description": "Get additional document chunks for more context",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "query": {"type": "string", "description": "Query for additional context"},
                                "k": {"type": "integer", "description": "Number of additional chunks"}
                            },
                            "required": ["query"]
                        }
                    }
                }
            ]

            # System prompt
            system_prompt = f"""You are a helpful AI assistant with access to document search tools.

Available context from initial search:
{context_text if context_text else "No relevant documents found in initial search."}

You can use tools to search for more specific information or get additional context.
Always be helpful and use the tools when you need more information."""

            # Call LLM with tools
            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                tools=tools,
                tool_choice="auto",
                max_tokens=800,
                temperature=0.7
            )

            # Handle tool calls if any
            if response.choices[0].message.tool_calls:
                final_response = self._handle_tools(message, response.choices[0].message)
            else:
                final_response = response.choices[0].message.content

            # Return with debug info
            return f"{debug_info}\n{final_response}"

        except Exception as e:
            return f"Error: {str(e)}"

    def _handle_tools(self, original_message: str, assistant_message) -> str:
        """Handle tool calls simply."""
        messages = [
            {"role": "user", "content": original_message},
            {"role": "assistant", "content": assistant_message.content, "tool_calls": assistant_message.tool_calls}
        ]

        # Execute tool calls
        for tool_call in assistant_message.tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)

            if function_name == "search_documents":
                result = self.search_documents(
                    function_args.get("query", ""),
                    function_args.get("k", 5)
                )
            elif function_name == "get_more_chunks":
                result = self.get_more_chunks(
                    function_args.get("query", ""),
                    function_args.get("k", 10)
                )
            else:
                result = "Unknown tool"

            messages.append({
                "role": "tool",
                "tool_call_id": tool_call.id,
                "content": result
            })

        # Get final response
        final_response = self.openai_client.chat.completions.create(
            model=self.model_name,
            messages=messages,
            max_tokens=800,
            temperature=0.7
        )

        return final_response.choices[0].message.content

    def get_stats(self) -> Dict:
        """Get simple stats about the agent."""
        return {
            "project_id": self.project_id,
            "model_name": self.model_name,
            "tools_available": len(self.tools),
            "tool_names": [t.__name__ for t in self.tools]
        }
    
    def get_stats(self) -> Dict:
        """Get statistics about the Graph-RAG system."""
        try:
            # Get vector store stats (this would need to be implemented in DocumentManager)
            # Get knowledge graph stats
            kg_results = self.document_manager.knowledge_graph_query()  # Get all triples
            
            return {
                "project_id": self.project_id,
                "model_name": self.model_name,
                "total_triples": len(kg_results),
                "tools_available": len(self.tools),
                "tool_names": [t.__name__ for t in self.tools]
            }
        except Exception as e:
            return {
                "error": str(e)
            }
