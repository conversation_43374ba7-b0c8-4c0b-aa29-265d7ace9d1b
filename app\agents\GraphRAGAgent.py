from typing import List, Callable, Dict, Any, Optional
from .BaseAgent import BaseAgent
from ..managers.DocumentManager import DocumentManager
import json
import os
from openai import OpenAI

class GraphRAGAgent(BaseAgent):
    """Graph-RAG Agent that extends BaseAgent with vector search and knowledge graph capabilities."""
    
    access_specifier: str = "General"
    model_name: str = "test"
    
    def __init__(self, project_id: str = None, model_name: str = None, access_specifier: str = None, db_config: Dict = None):
        # Initialize the document manager
        self.project_id = project_id
        self.document_manager = DocumentManager(project_id=project_id, db_config=db_config)

        # Initialize OpenAI client
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Define tools specific to Graph-RAG
        self.default_tools = [
            self.vector_search_tool,
            self.knowledge_graph_query_tool,
            self.hybrid_search_tool,
            self.process_document_tool
        ]

        # Call parent constructor
        super().__init__(model_name=model_name, access_specifier=access_specifier)
    
    def _build_system_prompt(self) -> str:
        return f"""You are a Graph-RAG AI assistant with access to both vector search and knowledge graph capabilities.

You have access to the following tools:
- vector_search: Search for similar document chunks using semantic similarity
- knowledge_graph_query: Query structured knowledge graph for factual relationships
- hybrid_search: Combine both vector and knowledge graph search
- process_document: Process new documents through the Graph-RAG pipeline

Use vector search for open-ended or contextual questions.
Use knowledge graph queries for factual questions about specific entities and relationships.
Use hybrid search when you need comprehensive information combining both approaches.

Model: {self.model_name}
Access Level: {self.access_specifier}
Project ID: {self.project_id}
"""
    
    def vector_search_tool(self, query: str, k: int = 5) -> str:
        """
        Search for similar document chunks using vector similarity.
        
        Args:
            query: The search query
            k: Number of results to return (default: 5)
        
        Returns:
            JSON string with search results
        """
        try:
            results = self.document_manager.vector_search(query, k)
            return json.dumps({
                "tool": "vector_search",
                "query": query,
                "results": results,
                "count": len(results)
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "vector_search",
                "error": str(e)
            })
    
    def knowledge_graph_query_tool(self, subject: str = None, predicate: str = None, obj: str = None) -> str:
        """
        Query the knowledge graph for specific relationships.
        
        Args:
            subject: Subject entity to search for
            predicate: Relationship/predicate to search for
            obj: Object entity to search for
        
        Returns:
            JSON string with knowledge graph results
        """
        try:
            results = self.document_manager.knowledge_graph_query(subject, predicate, obj)
            return json.dumps({
                "tool": "knowledge_graph_query",
                "query": {
                    "subject": subject,
                    "predicate": predicate,
                    "object": obj
                },
                "results": results,
                "count": len(results)
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "knowledge_graph_query",
                "error": str(e)
            })
    
    def hybrid_search_tool(self, query: str, k: int = 5) -> str:
        """
        Perform both vector search and knowledge graph query.
        
        Args:
            query: The search query
            k: Number of vector results to return
        
        Returns:
            JSON string with combined results
        """
        try:
            results = self.document_manager.hybrid_search(query, k)
            return json.dumps({
                "tool": "hybrid_search",
                "query": query,
                "vector_results": results["vector_results"],
                "knowledge_graph_results": results["knowledge_graph_results"],
                "total_vector_results": len(results["vector_results"]),
                "total_kg_results": len(results["knowledge_graph_results"])
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "hybrid_search",
                "error": str(e)
            })
    
    def process_document_tool(self, file_path: str, metadata: Dict = None) -> str:
        """
        Process a document through the Graph-RAG pipeline.
        
        Args:
            file_path: Path to the document to process
            metadata: Optional metadata to attach to the document
        
        Returns:
            JSON string with processing results
        """
        try:
            results = self.document_manager.process_document(file_path, metadata)
            return json.dumps({
                "tool": "process_document",
                "file_path": file_path,
                "metadata": metadata,
                "results": results
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "tool": "process_document",
                "error": str(e)
            })
    
    def reply(self, message: str, context: Dict[str, Any] = None) -> str:
        """Generate a proper response using Graph-RAG capabilities with real LLM."""
        try:
            # Perform hybrid search to get relevant information
            search_results = self.document_manager.hybrid_search(message)
            vector_results = search_results["vector_results"]

            # Build context from retrieved documents
            context_chunks = []
            if vector_results:
                for result in vector_results[:5]:  # Top 5 most relevant chunks
                    context_chunks.append(result['content'])

            # Generate response using OpenAI with retrieved context
            if context_chunks:
                return self._generate_llm_response_with_context(message, context_chunks)
            else:
                return self._generate_llm_response_no_context(message)

        except Exception as e:
            return f"Error processing your question: {str(e)}"

    def _generate_llm_response_with_context(self, message: str, context_chunks: List[str]) -> str:
        """Generate LLM response using retrieved context."""
        try:
            # Combine context chunks
            context_text = "\n\n".join(context_chunks)

            # Create system prompt
            system_prompt = f"""You are a helpful AI assistant with access to relevant documents.
Use the provided context to answer the user's question accurately and concisely.

Context from documents:
{context_text}

Instructions:
- Answer based on the provided context
- If the context doesn't contain enough information, say so
- Be specific and cite relevant details from the context
- Keep your response focused and helpful"""

            # Generate response using OpenAI
            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                max_tokens=500,
                temperature=0.7
            )

            return response.choices[0].message.content

        except Exception as e:
            return f"Error generating response: {str(e)}"

    def _generate_llm_response_no_context(self, message: str) -> str:
        """Generate LLM response when no relevant context is found."""
        try:
            system_prompt = f"""You are a helpful AI assistant for project {self.project_id}.
The user has asked a question, but no relevant documents were found in the knowledge base.

Instructions:
- Politely explain that you don't have specific information about their question
- Suggest they upload relevant documents to help you answer better
- Offer to help with general questions if appropriate"""

            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                max_tokens=200,
                temperature=0.7
            )

            return response.choices[0].message.content

        except Exception as e:
            return f"I don't have specific information about '{message}' in the current knowledge base. Please upload relevant documents first."

    def generate_response_with_tools(self, message: str) -> str:
        """Generate response with tool calling capabilities."""
        try:
            # Define available tools for the LLM
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "search_documents",
                        "description": "Search through uploaded documents for relevant information",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "string",
                                    "description": "The search query to find relevant information"
                                },
                                "max_results": {
                                    "type": "integer",
                                    "description": "Maximum number of results to return (default: 5)"
                                }
                            },
                            "required": ["query"]
                        }
                    }
                }
            ]

            system_prompt = f"""You are a helpful AI assistant for project {self.project_id} with access to a document search tool.

When users ask questions:
1. Use the search_documents tool to find relevant information
2. Provide comprehensive answers based on the search results
3. If no relevant information is found, explain this to the user

Always search for information before answering questions about specific topics."""

            # Initial LLM call with tools
            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                tools=tools,
                tool_choice="auto",
                max_tokens=1000,
                temperature=0.7
            )

            # Handle tool calls
            if response.choices[0].message.tool_calls:
                return self._handle_tool_calls(message, response.choices[0].message)
            else:
                return response.choices[0].message.content

        except Exception as e:
            # Fallback to regular response
            return self.reply(message)

    def _handle_tool_calls(self, original_message: str, assistant_message) -> str:
        """Handle tool calls from the LLM."""
        try:
            messages = [
                {"role": "system", "content": f"You are a helpful AI assistant for project {self.project_id}."},
                {"role": "user", "content": original_message},
                {"role": "assistant", "content": assistant_message.content, "tool_calls": assistant_message.tool_calls}
            ]

            # Execute each tool call
            for tool_call in assistant_message.tool_calls:
                function_name = tool_call.function.name
                function_args = json.loads(tool_call.function.arguments)

                if function_name == "search_documents":
                    query = function_args.get("query", "")
                    max_results = function_args.get("max_results", 5)

                    # Perform the search
                    search_results = self.document_manager.vector_search(query, max_results)

                    # Format results for the LLM
                    if search_results:
                        result_text = "Search Results:\n"
                        for i, result in enumerate(search_results, 1):
                            result_text += f"\n{i}. {result['content'][:300]}...\n"
                    else:
                        result_text = "No relevant documents found for this query."

                    # Add tool result to messages
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": result_text
                    })

            # Generate final response with tool results
            final_response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=800,
                temperature=0.7
            )

            return final_response.choices[0].message.content

        except Exception as e:
            return f"Error handling tool calls: {str(e)}"
    
    def get_stats(self) -> Dict:
        """Get statistics about the Graph-RAG system."""
        try:
            # Get vector store stats (this would need to be implemented in DocumentManager)
            # Get knowledge graph stats
            kg_results = self.document_manager.knowledge_graph_query()  # Get all triples
            
            return {
                "project_id": self.project_id,
                "model_name": self.model_name,
                "total_triples": len(kg_results),
                "tools_available": len(self.tools),
                "tool_names": [t.__name__ for t in self.tools]
            }
        except Exception as e:
            return {
                "error": str(e)
            }
