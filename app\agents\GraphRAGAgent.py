from typing import Callable, Dict, Any
from .BaseAgent import BaseAgent
from ..managers.DocumentManager import DocumentManager
import json
import os
from openai import OpenAI

class GraphRAGAgent(BaseAgent):
    """Graph-RAG Agent that extends BaseAgent with vector search and knowledge graph capabilities."""
    
    access_specifier: str = "General"
    model_name: str = "gpt-4"
    
    def __init__(self, project_id: str = None, model_name: str = None, access_specifier: str = None, db_config: Dict = None):
        self.project_id = project_id
        self.document_manager = DocumentManager(project_id=project_id, db_config=db_config)
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Define tools - simple and clean
        self.default_tools = [self.project_search]

        super().__init__(model_name=model_name, access_specifier=access_specifier)
    
    def _build_system_prompt(self) -> str:
        return f"""You are a helpful AI assistant with access to document search tools.

Available tools:
- search_documents: Search through uploaded documents
- get_more_chunks: Get additional document chunks if needed

Always search for relevant information before answering questions.
Model: {self.model_name} | Project: {self.project_id}
"""
    
    def project_search(self, query: str, k: int = 5) -> str:
        """Search through project documents and return results."""
        try:
            results = self.document_manager.vector_search(query, k)

            if not results:
                # Try a broader search with more results
                results = self.document_manager.vector_search(query, k=20)

            return json.dumps({
                "found": len(results),
                "query_used": query,
                "results": [{"content": r["content"], "score": r["score"], "metadata": r.get("metadata", {})} for r in results]
            })
        except Exception as e:
            return f"Search error: {str(e)}"
    
    def reply(self, message: str, context: Dict[str, Any] = None) -> str:
        """Force AI to search first, then respond knowledgeably."""
        try:
            # Define search tool for LLM
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "project_search",
                        "description": "Search through uploaded project documents for relevant information. Use this FIRST before answering any question.",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "query": {"type": "string", "description": "Search query to find relevant information"},
                                "k": {"type": "integer", "description": "Number of results to return (default: 5)"}
                            },
                            "required": ["query"]
                        }
                    }
                }
            ]

            # System prompt that forces searching
            system_prompt = f"""You are a knowledgeable AI assistant for project {self.project_id}.

IMPORTANT INSTRUCTIONS:
1. ALWAYS use project_search tool FIRST before answering any question
2. You can search multiple times (up to 15 searches) with different queries to get comprehensive information
3. If initial searches return 0 results, try different keywords, synonyms, or broader terms
4. After searching, respond as if you already know the information from the documents
5. Be confident and specific in your answers
6. If no relevant documents are found after multiple searches, politely explain that no information is available

Never say "I found" or "According to the search" - just answer directly as if you know the information."""

            # Call LLM with forced tool usage
            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                tools=tools,
                tool_choice="required",  # Force tool usage
                max_tokens=1000,
                temperature=0.7
            )

            # Handle tool calls (there will always be at least one)
            return self._handle_tools(message, response.choices[0].message)

        except Exception as e:
            return f"Error: {str(e)}"

    def _handle_tools(self, original_message: str, assistant_message) -> str:
        """Handle tool calls with debug info and support for multiple searches."""
        search_count = 0
        debug_info = []

        # System prompt for follow-up responses
        system_prompt = f"""You are a knowledgeable AI assistant for project {self.project_id}.

Based on the search results, provide a comprehensive and confident answer.
- Speak as if you already know this information
- Be specific and detailed
- Don't mention searching or tools
- If you need more information, you can search again with different keywords

You can search multiple times if needed to get complete information."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": original_message},
            {"role": "assistant", "content": assistant_message.content, "tool_calls": assistant_message.tool_calls}
        ]

        # Execute initial tool calls
        for tool_call in assistant_message.tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)

            if function_name == "project_search":
                search_count += 1
                query = function_args.get("query", "")
                k = function_args.get("k", 5)

                result = self.project_search(query, k)
                result_data = json.loads(result)

                debug_info.append(f"🔍 Search {search_count}: '{query}' → {result_data['found']} results")

                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": result
                })

        # Allow for multiple rounds of searching
        max_searches = 15
        while search_count < max_searches:
            # Get response (might include more tool calls)
            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                tools=[{
                    "type": "function",
                    "function": {
                        "name": "project_search",
                        "description": "Search for additional information if needed",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "query": {"type": "string", "description": "Search query"},
                                "k": {"type": "integer", "description": "Number of results (default: 5)"}
                            },
                            "required": ["query"]
                        }
                    }
                }],
                tool_choice="auto",
                max_tokens=1000,
                temperature=0.7
            )

            # If no more tool calls, we're done
            if not response.choices[0].message.tool_calls:
                final_answer = response.choices[0].message.content
                break

            # Handle additional searches
            messages.append({
                "role": "assistant",
                "content": response.choices[0].message.content,
                "tool_calls": response.choices[0].message.tool_calls
            })

            for tool_call in response.choices[0].message.tool_calls:
                if tool_call.function.name == "project_search":
                    search_count += 1
                    function_args = json.loads(tool_call.function.arguments)
                    query = function_args.get("query", "")
                    k = function_args.get("k", 5)

                    result = self.project_search(query, k)
                    result_data = json.loads(result)

                    debug_info.append(f"🔍 Search {search_count}: '{query}' → {result_data['found']} results")

                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": result
                    })
        else:
            # Max searches reached, get final response
            final_response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=1000,
                temperature=0.7
            )
            final_answer = final_response.choices[0].message.content

        # Combine debug info with final answer
        debug_summary = "\n".join(debug_info)
        return f"{debug_summary}\n\n{final_answer}"

    def get_stats(self) -> Dict:
        """Get simple stats about the agent."""
        try:
            return {
                "project_id": self.project_id,
                "model_name": self.model_name,
                "tools_available": len(self.tools),
                "tool_names": [t.__name__ for t in self.tools],
                "vector_store_available": self.document_manager.vector_store is not None
            }
        except Exception as e:
            return {
                "project_id": self.project_id,
                "model_name": self.model_name,
                "error": str(e)
            }
    
    def get_stats(self) -> Dict:
        """Get statistics about the Graph-RAG system."""
        try:
            # Get vector store stats (this would need to be implemented in DocumentManager)
            # Get knowledge graph stats
            kg_results = self.document_manager.knowledge_graph_query()  # Get all triples
            
            return {
                "project_id": self.project_id,
                "model_name": self.model_name,
                "total_triples": len(kg_results),
                "tools_available": len(self.tools),
                "tool_names": [t.__name__ for t in self.tools]
            }
        except Exception as e:
            return {
                "error": str(e)
            }
