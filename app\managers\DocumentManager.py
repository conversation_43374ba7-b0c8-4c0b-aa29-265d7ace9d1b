import os
import psycopg2
from typing import List, Dict, Optional, Tuple
import uuid
from datetime import datetime

# Simple Graph-RAG imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain_postgres import PGEngine, PGVectorStore
from langchain.schema import Document


class DocumentManager:
    """Simple document manager for Graph-RAG functionality."""

    def __init__(self, project_id: str = None, db_config: Dict = None):
        self.project_id = project_id

        # Default PostgreSQL configuration
        self.db_config = db_config or {
            "host": os.getenv("POSTGRES_HOST", "localhost"),
            "port": os.getenv("POSTGRES_PORT", "5432"),
            "database": os.getenv("POSTGRES_DB", "kairos_graphrag"),
            "user": os.getenv("POSTGRES_USER", "postgres"),
            "password": os.getenv("POSTGRES_PASSWORD", "password")
        }

        # Initialize components
        self.embedding_model = OpenAIEmbeddings(
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )

        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100,
        )

        # Connection string for new langchain-postgres
        self.connection_string = f"postgresql+psycopg://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"

        # Initialize database tables first
        self._init_db()

        # Initialize vector store with new implementation
        self.table_name = f"embeddings_{self.project_id}".replace("-", "_") if self.project_id else "embeddings"

        try:
            # Create engine
            self.engine = PGEngine.from_connection_string(url=self.connection_string)

            # Initialize vectorstore table
            self.engine.init_vectorstore_table(
                table_name=self.table_name,
                vector_size=1536,  # OpenAI embedding size
            )

            # Create vector store
            self.vector_store = PGVectorStore.create_sync(
                engine=self.engine,
                table_name=self.table_name,
                embedding_service=self.embedding_model,
            )
        except Exception as e:
            print(f"Vector store initialization failed: {e}")
            self.vector_store = None
    
    def _sanitize_table_name(self, base_name: str, project_id: str = None) -> str:
        """
        Sanitize table name by replacing invalid characters.
        PostgreSQL table names cannot contain hyphens.
        
        Args:
            base_name: Base table name (e.g., 'triples', 'documents')
            project_id: Optional project ID to append
            
        Returns:
            Sanitized table name safe for PostgreSQL
        """
        if project_id:
            # Replace hyphens and other invalid chars with underscores
            sanitized_id = project_id.replace("-", "_").replace(" ", "_")
            return f"{base_name}_{sanitized_id}"
        return base_name
    
    def _get_triples_table_name(self) -> str:
        """Get the sanitized triples table name for this project."""
        return self._sanitize_table_name("triples", self.project_id)
    
    def _reset_vector_store_tables(self):
        """Reset vector store tables to match new schema."""
        try:
            import psycopg2
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
            
            # Get collection name
            collection_name = f"documents_{self.project_id}".replace("-", "_") if self.project_id else "documents"
            
            # Drop existing tables if they exist (they might have old schema)
            # Try different possible table names from different langchain versions
            tables_to_drop = [
                "langchain_pg_embedding",
                "langchain_pg_collection", 
                f"langchain_pg_embedding_{collection_name}",
                f"langchain_pg_collection_{collection_name}",
                collection_name,
                f"{collection_name}_embedding",
                f"{collection_name}_collection"
            ]
            
            for table in tables_to_drop:
                try:
                    cur.execute(f"DROP TABLE IF EXISTS {table} CASCADE;")
                except Exception as e:
                    # Some tables might not exist, continue
                    pass
                    
            conn.commit()
            cur.close()
            conn.close()
            
            print("Reset vector store tables for new schema")
            
        except Exception as e:
            print(f"Warning: Could not reset vector store tables: {e}")

    def _init_db(self):
        """Initialize PostgreSQL database with pgvector extension."""
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()

            # Create pgvector extension
            cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")

            conn.commit()
            cur.close()
            conn.close()

        except Exception as e:
            print(f"Database initialization error: {e}")
    
    def process_text_content(self, content: str, filename: str, metadata: Dict = None) -> Dict:
        """Process text content through the Graph-RAG pipeline."""
        try:
            if not self.vector_store:
                return {
                    "status": "error",
                    "error": "Vector store not initialized"
                }

            # Chunk the document
            chunks = self.text_splitter.split_text(content)

            # Create documents with metadata
            docs = []
            chunk_ids = []
            for i, chunk in enumerate(chunks):
                chunk_id = str(uuid.uuid4())
                chunk_ids.append(chunk_id)

                doc_metadata = {
                    "chunk_id": chunk_id,
                    "chunk_index": i,
                    "source_file": filename,
                    "project_id": self.project_id,
                    **(metadata or {})
                }
                docs.append(Document(page_content=chunk, metadata=doc_metadata))

            # Store embeddings in vector database
            self.vector_store.add_documents(docs)

            return {
                "status": "success",
                "chunks_processed": len(chunks),
                "chunk_ids": chunk_ids
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    

    
    def vector_search(self, query: str, k: int = 5) -> List[Dict]:
        """Perform vector similarity search."""
        try:
            if not self.vector_store:
                return []
            results = self.vector_store.similarity_search_with_score(query, k=k)
            return [
                {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "score": score
                }
                for doc, score in results
            ]
        except Exception as e:
            print(f"Vector search error: {e}")
            return []

    def hybrid_search(self, query: str, k: int = 5) -> Dict:
        """Perform vector search only (simplified)."""
        vector_results = self.vector_search(query, k)

        return {
            "vector_results": vector_results,
            "knowledge_graph_results": []  # Simplified - no KG for now
        }
    
    def process_file_upload(self, file_content: bytes, filename: str, metadata: Dict = None) -> Dict:
        """Simple file processing - just handle text content."""
        try:
            # Try to decode as text
            if isinstance(file_content, bytes):
                content = file_content.decode('utf-8')
            else:
                content = str(file_content)

            # Process the text content
            return self.process_text_content(content, filename, metadata)

        except Exception as e:
            return {
                "status": "error",
                "error": f"File processing failed: {str(e)}"
            }
