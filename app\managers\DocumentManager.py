import os
import psycopg2
from typing import List, Dict, Optional, <PERSON><PERSON>
from pathlib import Path
import uuid
from datetime import datetime

# Graph-RAG imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain.vectorstores import PGVector  # Use the stable version for now
from langchain.schema import Document
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader, TextLoader
from langchain_community.document_loaders import UnstructuredFileLoader
import spacy
import dotenv
import tempfile
import io

# Load environment variables from .env file
dotenv.load_dotenv()

class DocumentManager:
    """Document manager for Graph-RAG functionality following the existing manager pattern."""
    
    def __init__(self, project_id: str = None, db_config: Dict = None):
        self.project_id = project_id
        
        # Default PostgreSQL configuration
        self.db_config = db_config or {
            "host": os.getenv("POSTGRES_HOST"),
            "port": os.getenv("POSTGRES_PORT"),
            "user": os.getenv("POSTGRES_USER"),
            "password": os.getenv("POSTGRES_PASSWORD", None),
            "database": os.getenv("POSTGRES_DB"),
        }
        
        # Initialize components
        self.embedding_model = OpenAIEmbeddings(
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100,
        )
        
        # Initialize spaCy for knowledge graph extraction
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            # If model not found, we'll handle this gracefully
            self.nlp = None
        
        # Connection string for pgvector
        self.connection_string = f"postgresql+psycopg2://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
        
        # Initialize database tables first
        self._init_db()
        
        # Initialize vector store with proper table setup
        collection_name = f"documents_{self.project_id}".replace("-", "_") if self.project_id else "documents"
        
        # Initialize vector store
        self.vector_store = PGVector(
            connection_string=self.connection_string,
            collection_name=collection_name,
            embedding_function=self.embedding_model,
        )
    
    def _sanitize_table_name(self, base_name: str, project_id: str = None) -> str:
        """
        Sanitize table name by replacing invalid characters.
        PostgreSQL table names cannot contain hyphens.
        
        Args:
            base_name: Base table name (e.g., 'triples', 'documents')
            project_id: Optional project ID to append
            
        Returns:
            Sanitized table name safe for PostgreSQL
        """
        if project_id:
            # Replace hyphens and other invalid chars with underscores
            sanitized_id = project_id.replace("-", "_").replace(" ", "_")
            return f"{base_name}_{sanitized_id}"
        return base_name
    
    def _get_triples_table_name(self) -> str:
        """Get the sanitized triples table name for this project."""
        return self._sanitize_table_name("triples", self.project_id)
    
    def _reset_vector_store_tables(self):
        """Reset vector store tables to match new schema."""
        try:
            import psycopg2
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
            
            # Get collection name
            collection_name = f"documents_{self.project_id}".replace("-", "_") if self.project_id else "documents"
            
            # Drop existing tables if they exist (they might have old schema)
            # Try different possible table names from different langchain versions
            tables_to_drop = [
                "langchain_pg_embedding",
                "langchain_pg_collection", 
                f"langchain_pg_embedding_{collection_name}",
                f"langchain_pg_collection_{collection_name}",
                collection_name,
                f"{collection_name}_embedding",
                f"{collection_name}_collection"
            ]
            
            for table in tables_to_drop:
                try:
                    cur.execute(f"DROP TABLE IF EXISTS {table} CASCADE;")
                except Exception as e:
                    # Some tables might not exist, continue
                    pass
                    
            conn.commit()
            cur.close()
            conn.close()
            
            print("Reset vector store tables for new schema")
            
        except Exception as e:
            print(f"Warning: Could not reset vector store tables: {e}")

    def _init_db(self):
        """Initialize PostgreSQL database with pgvector extension and required tables."""
        try:
            import psycopg2
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
            
            # Create pgvector extension
            cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            
            # Reset vector store tables first (to handle schema changes)
            cur.close()
            conn.close()
            self._reset_vector_store_tables()
            
            # Reconnect for triples table
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
            
            # Create triples table for knowledge graph
            table_name = self._get_triples_table_name()
            cur.execute(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id SERIAL PRIMARY KEY,
                    subject TEXT NOT NULL,
                    predicate TEXT NOT NULL,
                    object TEXT NOT NULL,
                    chunk_id TEXT,
                    confidence FLOAT DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Create indexes for faster queries
            cur.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_{table_name}_subject 
                ON {table_name} (subject);
            """)
            cur.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_{table_name}_predicate 
                ON {table_name} (predicate);
            """)
            
            conn.commit()
            cur.close()
            conn.close()
            
        except Exception as e:
            print(f"Database initialization error: {e}")
    
    def process_document(self, file_path: str, metadata: Dict = None) -> Dict:
        """Process a document through the Graph-RAG pipeline."""
        try:
            # Read document
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Chunk the document
            chunks = self.text_splitter.split_text(content)
            
            # Create documents with metadata
            docs = []
            chunk_ids = []
            for i, chunk in enumerate(chunks):
                chunk_id = str(uuid.uuid4())
                chunk_ids.append(chunk_id)
                
                doc_metadata = {
                    "chunk_id": chunk_id,
                    "chunk_index": i,
                    "source_file": file_path,
                    "project_id": self.project_id,
                    **(metadata or {})
                }
                docs.append(Document(page_content=chunk, metadata=doc_metadata))
            
            # Store embeddings in vector database
            self.vector_store.add_documents(docs)
            
            # Extract and store knowledge graph triples
            triples_count = 0
            for chunk, chunk_id in zip(chunks, chunk_ids):
                triples = self.extract_triples(chunk)
                self._store_triples(triples, chunk_id)
                triples_count += len(triples)
            
            return {
                "status": "success",
                "chunks_processed": len(chunks),
                "triples_extracted": triples_count,
                "chunk_ids": chunk_ids
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def extract_triples(self, text: str) -> List[Tuple[str, str, str]]:
        """Extract knowledge graph triples from text using spaCy."""
        if not self.nlp:
            return []
        
        doc = self.nlp(text)
        triples = []
        
        for sent in doc.sents:
            subj = ""
            obj = ""
            verb = ""
            
            for token in sent:
                if "subj" in token.dep_:
                    subj = token.text
                if "obj" in token.dep_:
                    obj = token.text
                if token.pos_ == "VERB":
                    verb = token.lemma_
            
            if subj and verb and obj:
                triples.append((subj, verb, obj))
        
        return triples
    
    def _store_triples(self, triples: List[Tuple[str, str, str]], chunk_id: str):
        """Store triples in PostgreSQL."""
        if not triples:
            return
    
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
        
            table_name = self._get_triples_table_name()
        
            for subject, predicate, obj in triples:
                cur.execute(f"""
                    INSERT INTO {table_name} (subject, predicate, object, chunk_id) 
                    VALUES (%s, %s, %s, %s)
                """, (subject, predicate, obj, chunk_id))
        
            conn.commit()
            cur.close()
            conn.close()
        
        except Exception as e:
            print(f"Error storing triples: {e}")
    
    def vector_search(self, query: str, k: int = 5) -> List[Dict]:
        """Perform vector similarity search."""
        try:
            results = self.vector_store.similarity_search_with_score(query, k=k)
            return [
                {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "score": score
                }
                for doc, score in results
            ]
        except Exception as e:
            return []
    
    def knowledge_graph_query(self, subject: str = None, predicate: str = None, obj: str = None) -> List[Dict]:
        """Query the knowledge graph for triples."""
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
        
            table_name = self._get_triples_table_name()
        
            # Build dynamic query
            conditions = []
            params = []
            
            if subject:
                conditions.append("subject ILIKE %s")
                params.append(f"%{subject}%")
            if predicate:
                conditions.append("predicate ILIKE %s")
                params.append(f"%{predicate}%")
            if obj:
                conditions.append("object ILIKE %s")
                params.append(f"%{obj}%")
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            cur.execute(f"""
                SELECT subject, predicate, object, chunk_id, confidence 
                FROM {table_name} 
                WHERE {where_clause}
                ORDER BY confidence DESC
                LIMIT 20
            """, params)
            
            results = cur.fetchall()
            cur.close()
            conn.close()
            
            return [
                {
                    "subject": row[0],
                    "predicate": row[1],
                    "object": row[2],
                    "chunk_id": row[3],
                    "confidence": row[4]
                }
                for row in results
            ]
            
        except Exception as e:
            print(f"Knowledge graph query error: {e}")
            return []
    
    def hybrid_search(self, query: str, k: int = 5) -> Dict:
        """Perform both vector search and knowledge graph query."""
        vector_results = self.vector_search(query, k)
        
        # Extract potential entities from query for KG search
        kg_results = []
        if self.nlp:
            doc = self.nlp(query)
            for ent in doc.ents:
                kg_results.extend(self.knowledge_graph_query(subject=ent.text))
                kg_results.extend(self.knowledge_graph_query(obj=ent.text))
        
        return {
            "vector_results": vector_results,
            "knowledge_graph_results": kg_results[:10]  # Limit KG results
        }
    
    def process_file_upload(self, file_content: bytes, filename: str, metadata: Dict = None) -> Dict:
        """
        Process uploaded file content through Graph-RAG pipeline using LangChain loaders.
        Handles different file types (PDF, DOCX, TXT, etc.)
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            metadata: Additional metadata for the document
            
        Returns:
            Processing result dictionary
        """
        try:
            # Create temporary file with original content
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{self._get_file_extension(filename)}") as tmp_file:
                tmp_file.write(file_content)
                tmp_file_path = tmp_file.name
            
            try:
                # Load document using appropriate LangChain loader
                documents = self._load_document_with_langchain(tmp_file_path, filename)
                
                # Process documents through Graph-RAG pipeline
                return self._process_langchain_documents(documents, filename, metadata)
                
            finally:
                # Clean up temporary file
                if os.path.exists(tmp_file_path):
                    os.unlink(tmp_file_path)
                    
        except Exception as e:
            return {
                "status": "error",
                "error": f"File processing failed: {str(e)}"
            }
    
    def _load_document_with_langchain(self, file_path: str, filename: str) -> List[Document]:
        """Load document using appropriate LangChain loader."""
        file_extension = self._get_file_extension(filename)
        
        try:
            if file_extension == 'pdf':
                loader = PyPDFLoader(file_path)
            elif file_extension in ['docx', 'doc']:
                loader = Docx2txtLoader(file_path)
            elif file_extension in ['txt', 'md']:
                loader = TextLoader(file_path, encoding='utf-8')
            elif file_extension in ['csv', 'json', 'xml', 'html']:
                # UnstructuredFileLoader handles many formats
                loader = UnstructuredFileLoader(file_path)
            else:
                # Fallback to unstructured loader for other formats
                loader = UnstructuredFileLoader(file_path)
            
            documents = loader.load()
            
            if not documents:
                raise ValueError(f"No content could be extracted from {filename}")
            
            return documents
            
        except Exception as e:
            raise ValueError(f"Failed to load {filename}: {str(e)}")
    
    def _process_langchain_documents(self, documents: List[Document], filename: str, metadata: Dict = None) -> Dict:
        """Process LangChain documents through Graph-RAG pipeline."""
        try:
            # Combine all document pages/chunks into single text for splitting
            combined_text = "\n\n".join([doc.page_content for doc in documents])
            
            # Split into chunks
            chunks = self.text_splitter.split_text(combined_text)
            
            # Create documents with metadata
            processed_docs = []
            chunk_ids = []
            
            for i, chunk in enumerate(chunks):
                chunk_id = str(uuid.uuid4())
                chunk_ids.append(chunk_id)
                
                # Combine original metadata with our metadata
                doc_metadata = {
                    "chunk_id": chunk_id,
                    "chunk_index": i,
                    "source_file": filename,
                    "project_id": self.project_id,
                }
                
                # Add original document metadata if available
                if documents and documents[0].metadata:
                    doc_metadata.update(documents[0].metadata)
                
                # Add user-provided metadata
                if metadata:
                    doc_metadata.update(metadata)
                
                processed_docs.append(Document(page_content=chunk, metadata=doc_metadata))
            
            # Store embeddings in vector database
            self.vector_store.add_documents(processed_docs)
            
            # Extract and store knowledge graph triples
            triples_count = 0
            for chunk, chunk_id in zip(chunks, chunk_ids):
                triples = self.extract_triples(chunk)
                self._store_triples(triples, chunk_id)
                triples_count += len(triples)
            
            return {
                "status": "success",
                "filename": filename,
                "chunks_processed": len(chunks),
                "triples_extracted": triples_count,
                "chunk_ids": chunk_ids,
                "original_documents": len(documents)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"Document processing failed: {str(e)}"
            }
    
    def _get_file_extension(self, filename: str) -> str:
        """Get file extension in lowercase."""
        return filename.lower().split('.')[-1] if '.' in filename else ''
    
    def get_supported_file_types(self) -> List[str]:
        """Get list of supported file types."""
        return [
            'pdf', 'docx', 'doc', 'txt', 'md', 'csv', 
            'json', 'xml', 'html', 'rtf', 'odt'
        ]
    
    def is_supported_file_type(self, filename: str) -> bool:
        """Check if file type is supported."""
        file_extension = self._get_file_extension(filename)
        return file_extension in self.get_supported_file_types()
