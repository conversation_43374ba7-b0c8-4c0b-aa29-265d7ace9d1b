import os
import psycopg2
from typing import List, Dict, Optional, Tu<PERSON>
from pathlib import Path
import uuid
from datetime import datetime

# Graph-RAG imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain_postgres import PGVector
from langchain.schema import Document
import spacy
import dotenv

# Load environment variables from .env file
dotenv.load_dotenv()

class DocumentManager:
    """Document manager for Graph-RAG functionality following the existing manager pattern."""
    
    def __init__(self, project_id: str = None, db_config: Dict = None):
        self.project_id = project_id
        
        # Default PostgreSQL configuration
        self.db_config = db_config or {
            "host": os.getenv("POSTGRES_HOST"),
            "port": os.getenv("POSTGRES_PORT"),
            "user": os.getenv("POSTGRES_USER"),
            "password": os.getenv("POSTGRES_PASSWORD", None),
            "database": os.getenv("POSTGRES_DB"),
        }
        
        # Initialize components
        self.embedding_model = OpenAIEmbeddings(
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100,
        )
        
        # Initialize spaCy for knowledge graph extraction
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            # If model not found, we'll handle this gracefully
            self.nlp = None
        
        # Connection string for pgvector
        self.connection_string = f"postgresql+psycopg2://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
        
        # Initialize vector store
        self.vector_store = PGVector(
        embeddings=self.embedding_model,
        connection=self.connection_string,
        collection_name=f"documents_{self.project_id}" if self.project_id else "documents",
         )
        
        # Initialize database tables
        self._init_db()
    
    def _sanitize_table_name(self, base_name: str, project_id: str = None) -> str:
        """
        Sanitize table name by replacing invalid characters.
        PostgreSQL table names cannot contain hyphens.
        
        Args:
            base_name: Base table name (e.g., 'triples', 'documents')
            project_id: Optional project ID to append
            
        Returns:
            Sanitized table name safe for PostgreSQL
        """
        if project_id:
            # Replace hyphens and other invalid chars with underscores
            sanitized_id = project_id.replace("-", "_").replace(" ", "_")
            return f"{base_name}_{sanitized_id}"
        return base_name
    
    def _get_triples_table_name(self) -> str:
        """Get the sanitized triples table name for this project."""
        return self._sanitize_table_name("triples", self.project_id)
    
    def _init_db(self):
        """Initialize PostgreSQL database with pgvector extension and triples table."""
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
            
            # Create pgvector extension
            cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            
            # Create triples table for knowledge graph
            table_name = self._get_triples_table_name()
            cur.execute(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id SERIAL PRIMARY KEY,
                    subject TEXT NOT NULL,
                    predicate TEXT NOT NULL,
                    object TEXT NOT NULL,
                    chunk_id TEXT,
                    confidence FLOAT DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Create indexes for faster queries
            cur.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_{table_name}_subject 
                ON {table_name} (subject);
            """)
            cur.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_{table_name}_predicate 
                ON {table_name} (predicate);
            """)
            
            conn.commit()
            cur.close()
            conn.close()
            
        except Exception as e:
            print(f"Database initialization error: {e}")
    
    def process_document(self, file_path: str, metadata: Dict = None) -> Dict:
        """Process a document through the Graph-RAG pipeline."""
        try:
            # Read document
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Chunk the document
            chunks = self.text_splitter.split_text(content)
            
            # Create documents with metadata
            docs = []
            chunk_ids = []
            for i, chunk in enumerate(chunks):
                chunk_id = str(uuid.uuid4())
                chunk_ids.append(chunk_id)
                
                doc_metadata = {
                    "chunk_id": chunk_id,
                    "chunk_index": i,
                    "source_file": file_path,
                    "project_id": self.project_id,
                    **(metadata or {})
                }
                docs.append(Document(page_content=chunk, metadata=doc_metadata))
            
            # Store embeddings in vector database
            self.vector_store.add_documents(docs)
            
            # Extract and store knowledge graph triples
            triples_count = 0
            for chunk, chunk_id in zip(chunks, chunk_ids):
                triples = self.extract_triples(chunk)
                self._store_triples(triples, chunk_id)
                triples_count += len(triples)
            
            return {
                "status": "success",
                "chunks_processed": len(chunks),
                "triples_extracted": triples_count,
                "chunk_ids": chunk_ids
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def extract_triples(self, text: str) -> List[Tuple[str, str, str]]:
        """Extract knowledge graph triples from text using spaCy."""
        if not self.nlp:
            return []
        
        doc = self.nlp(text)
        triples = []
        
        for sent in doc.sents:
            subj = ""
            obj = ""
            verb = ""
            
            for token in sent:
                if "subj" in token.dep_:
                    subj = token.text
                if "obj" in token.dep_:
                    obj = token.text
                if token.pos_ == "VERB":
                    verb = token.lemma_
            
            if subj and verb and obj:
                triples.append((subj, verb, obj))
        
        return triples
    
    def _store_triples(self, triples: List[Tuple[str, str, str]], chunk_id: str):
        """Store triples in PostgreSQL."""
        if not triples:
            return
    
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
        
            table_name = self._get_triples_table_name()
        
            for subject, predicate, obj in triples:
                cur.execute(f"""
                    INSERT INTO {table_name} (subject, predicate, object, chunk_id) 
                    VALUES (%s, %s, %s, %s)
                """, (subject, predicate, obj, chunk_id))
        
            conn.commit()
            cur.close()
            conn.close()
        
        except Exception as e:
            print(f"Error storing triples: {e}")
    
    def vector_search(self, query: str, k: int = 5) -> List[Dict]:
        """Perform vector similarity search."""
        try:
            results = self.vector_store.similarity_search_with_score(query, k=k)
            return [
                {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "score": score
                }
                for doc, score in results
            ]
        except Exception as e:
            return []
    
    def knowledge_graph_query(self, subject: str = None, predicate: str = None, obj: str = None) -> List[Dict]:
        """Query the knowledge graph for triples."""
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
        
            table_name = self._get_triples_table_name()
        
            # Build dynamic query
            conditions = []
            params = []
            
            if subject:
                conditions.append("subject ILIKE %s")
                params.append(f"%{subject}%")
            if predicate:
                conditions.append("predicate ILIKE %s")
                params.append(f"%{predicate}%")
            if obj:
                conditions.append("object ILIKE %s")
                params.append(f"%{obj}%")
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            cur.execute(f"""
                SELECT subject, predicate, object, chunk_id, confidence 
                FROM {table_name} 
                WHERE {where_clause}
                ORDER BY confidence DESC
                LIMIT 20
            """, params)
            
            results = cur.fetchall()
            cur.close()
            conn.close()
            
            return [
                {
                    "subject": row[0],
                    "predicate": row[1],
                    "object": row[2],
                    "chunk_id": row[3],
                    "confidence": row[4]
                }
                for row in results
            ]
            
        except Exception as e:
            print(f"Knowledge graph query error: {e}")
            return []
    
    def hybrid_search(self, query: str, k: int = 5) -> Dict:
        """Perform both vector search and knowledge graph query."""
        vector_results = self.vector_search(query, k)
        
        # Extract potential entities from query for KG search
        kg_results = []
        if self.nlp:
            doc = self.nlp(query)
            for ent in doc.ents:
                kg_results.extend(self.knowledge_graph_query(subject=ent.text))
                kg_results.extend(self.knowledge_graph_query(obj=ent.text))
        
        return {
            "vector_results": vector_results,
            "knowledge_graph_results": kg_results[:10]  # Limit KG results
        }
