from fastapi import APIRouter, WebSocket, WebSocketDisconnect, UploadFile, File, HTTPException
from app.managers.ProjectManager import ProjectManager
from app.managers.FileManager import FileManager
from pathlib import Path

router = APIRouter()

@router.websocket("/ws/project/{project_id}")
async def project_ws(websocket: WebSocket, project_id: str):
    project = ProjectManager.from_project_id(project_id, base_dir="./projects")
    if not project:
        await websocket.accept()
        await websocket.send_text("Project does not exist or is unavailable.")
        await websocket.close(code=4001)
        return
    await websocket.accept()
    try:
        while True:
            data = await websocket.receive_text()
            response = project.talk(data, role="user")
            await websocket.send_text(str(response))
    except WebSocketDisconnect:
        pass  # ProjectManager will be cleaned up automatically

@router.post("/project/{project_id}/upload")
async def upload_input_file(project_id: str, file: UploadFile = File(...)):
    print(f"Uploading file {file.filename} to project {project_id}")
    try:
        project_path = Path("./projects") / project_id
        if not project_path.exists():
            raise HTTPException(status_code=404, detail="Project not found")
        file_manager = FileManager(project_path)
        content = await file.read()
        file_manager.save_file(file.filename, content, location="input")
        return {"filename": file.filename, "status": "uploaded to input folder"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}") 