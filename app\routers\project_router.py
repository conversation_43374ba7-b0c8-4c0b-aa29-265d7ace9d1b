from fastapi import APIRouter, WebSocket, WebSocketDisconnect, UploadFile, File, HTTPException
from app.managers.ProjectManager import ProjectManager

router = APIRouter()

@router.websocket("/ws/project/{project_id}")
async def project_ws(websocket: WebSocket, project_id: str):
    project = ProjectManager.from_project_id(project_id, base_dir="./projects")
    if not project:
        await websocket.accept()
        await websocket.send_text("Project does not exist or is unavailable.")
        await websocket.close(code=4001)
        return
    await websocket.accept()
    try:
        while True:
            data = await websocket.receive_text()
            response = project.talk(data, role="user")
            await websocket.send_text(str(response))
    except WebSocketDisconnect:
        pass  # ProjectManager will be cleaned up automatically

@router.post("/project/{project_id}/upload")
async def upload_input_file(project_id: str, file: UploadFile = File(...)):
    print(f"Uploading file {file.filename} to project {project_id}")
    try:
        # Get the project using existing pattern
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Read file content
        content = await file.read()

        # Save file to input folder (existing functionality)
        saved_path = project.save_file(file.filename, content, location="input")

        # Process through Graph-RAG if agent supports it
        graphrag_result = None
        try:
            graphrag_agent = project.agent_manager.get_agent("graphrag")
            if graphrag_agent and hasattr(graphrag_agent, 'document_manager'):
                # Check if file type is supported
                if graphrag_agent.document_manager.is_supported_file_type(file.filename):
                    # Process file through DocumentManager using LangChain loaders
                    result = graphrag_agent.document_manager.process_file_upload(
                        content,
                        file.filename,
                        {"project_id": project_id}
                    )
                    graphrag_result = result
                else:
                    graphrag_result = {
                        "status": "skipped",
                        "reason": f"Unsupported file type. Supported types: {graphrag_agent.document_manager.get_supported_file_types()}"
                    }
        except Exception as e:
            print(f"Graph-RAG processing failed: {e}")
            graphrag_result = {
                "status": "error",
                "error": str(e)
            }

        response = {
            "filename": file.filename,
            "status": "uploaded to input folder",
            "saved_path": saved_path
        }

        if graphrag_result:
            response["graphrag"] = graphrag_result

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")

@router.post("/project/{project_id}/search")
async def search_project(project_id: str, query: str):
    """Simple search endpoint for Graph-RAG queries."""
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Use the agent's reply method which will automatically use Graph-RAG
        response = project.agent_manager.generate_response(query)

        return {
            "query": query,
            "response": response,
            "project_id": project_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")